<?php
// dashboard.php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Set page title
$pageTitle = 'DASHBOARD OVERVIEW';

// Get counts for dashboard
$agentCount = $pdo->query("SELECT COUNT(*) FROM agents")->fetchColumn();
$clientCount = $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn();
$maleClients = $pdo->query("SELECT COUNT(*) FROM clients WHERE gender = 'Male'")->fetchColumn();
$femaleClients = $pdo->query("SELECT COUNT(*) FROM clients WHERE gender = 'Female'")->fetchColumn();

// Get policy counts
$activePolicies = $pdo->query("SELECT COUNT(*) FROM policies WHERE status = 'Active'")->fetchColumn();
$pendingPolicies = $pdo->query("SELECT COUNT(*) FROM policies WHERE status = 'Pending'")->fetchColumn();
$lapsePolicies = $pdo->query("SELECT COUNT(*) FROM policies WHERE status = 'Lapse'")->fetchColumn();
$totalPolicies = $pdo->query("SELECT COUNT(*) FROM policies")->fetchColumn();

// Get additional statistics
$clientsWithDocuments = $pdo->query("SELECT COUNT(*) FROM clients WHERE nric IS NOT NULL AND signature IS NOT NULL AND bankcard IS NOT NULL")->fetchColumn();
$clientsWithoutDocuments = $pdo->query("SELECT COUNT(*) FROM clients WHERE nric IS NULL OR signature IS NULL OR bankcard IS NULL")->fetchColumn();
$agentsWithPolicies = $pdo->query("SELECT COUNT(DISTINCT agent_id) FROM policies WHERE agent_id IS NOT NULL")->fetchColumn();

// Get recent agents and clients (with email from users table)
$recentAgents = $pdo->query("SELECT a.*, u.email
                            FROM agents a
                            LEFT JOIN users u ON a.user_id = u.id
                            ORDER BY a.created_at DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
$recentClients = $pdo->query("SELECT * FROM clients ORDER BY created_at DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);

// Include layout header
include 'layout.php';
?>

<style>
.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: 10px;
    margin-left: 285px;
}
</style>

<!-- Dashboard Cards -->
<div class="dashboard-cards">
    <div class="dashboard-card">
        <h3>TOTAL AGENTS</h3>
        <div class="value"><?php echo $agentCount; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>TOTAL CLIENTS</h3>
        <div class="value"><?php echo $clientCount; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>MALE CLIENTS</h3>
        <div class="value"><?php echo $maleClients; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>FEMALE CLIENTS</h3>
        <div class="value"><?php echo $femaleClients; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>ACTIVE POLICIES</h3>
        <div class="value"><?php echo $activePolicies; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>PENDING APPROVALS</h3>
        <div class="value"><?php echo $pendingPolicies; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>TOTAL POLICIES</h3>
        <div class="value"><?php echo $totalPolicies; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>COMPLETE DOCUMENTS</h3>
        <div class="value"><?php echo $clientsWithDocuments; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>INCOMPLETE DOCUMENTS</h3>
        <div class="value"><?php echo $clientsWithoutDocuments; ?></div>
    </div>
    <div class="dashboard-card">
        <h3>AGENTS WITH POLICIES</h3>
        <div class="value"><?php echo $agentsWithPolicies; ?></div>
    </div>
</div>

<!-- Policy Status Details -->
<div class="row">
    <div class="col-md-12">
        <div class="chart-container">
            <div class="chart-title">Policy Status Details</div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Active</td>
                            <td><span class="badge badge-success"><?php echo $activePolicies; ?></span></td>
                        </tr>
                        <tr>
                            <td>Pending</td>
                            <td><span class="badge badge-warning"><?php echo $pendingPolicies; ?></span></td>
                        </tr>
                        <tr>
                            <td>Lapse</td>
                            <td><span class="badge badge-danger"><?php echo $lapsePolicies; ?></span></td>
                        </tr>
                        <tr class="table-info">
                            <td><strong>Total</strong></td>
                            <td><strong><?php echo $totalPolicies; ?></strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Document Completion Statistics -->
<div class="row">
    <div class="col-md-12">
        <div class="chart-container">
            <div class="chart-title">Document Completion Status</div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Document Status</th>
                            <th>Count</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Complete Documents</td>
                            <td><span class="badge badge-success"><?php echo $clientsWithDocuments; ?></span></td>
                            <td><?php echo $clientCount > 0 ? round(($clientsWithDocuments / $clientCount) * 100, 1) : 0; ?>%</td>
                        </tr>
                        <tr>
                            <td>Incomplete Documents</td>
                            <td><span class="badge badge-warning"><?php echo $clientsWithoutDocuments; ?></span></td>
                            <td><?php echo $clientCount > 0 ? round(($clientsWithoutDocuments / $clientCount) * 100, 1) : 0; ?>%</td>
                        </tr>
                        <tr class="table-info">
                            <td><strong>Total Clients</strong></td>
                            <td><strong><?php echo $clientCount; ?></strong></td>
                            <td><strong>100%</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-title">Recent Agents</div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentAgents)): ?>
                            <tr>
                                <td colspan="4" class="text-center">No agents found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recentAgents as $agent): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($agent['name'] ?? 'No name'); ?></td>
                                    <td><?php echo htmlspecialchars($agent['email'] ?? 'No email'); ?></td>
                                    <td><?php echo htmlspecialchars($agent['phone_number'] ?? 'No phone'); ?></td>
                                    <td>
                                        <a href="agent_view.php?id=<?php echo $agent['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="chart-container">
            <div class="chart-title">Recent Clients</div>
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recentClients)): ?>
                            <tr>
                                <td colspan="4" class="text-center">No clients found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recentClients as $client): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($client['name'] ?? 'No name'); ?></td>
                                    <td><?php echo htmlspecialchars($client['email'] ?? 'No email'); ?></td>
                                    <td><?php echo htmlspecialchars($client['phone_number'] ?? 'No phone'); ?></td>
                                    <td>
                                        <a href="client_view.php?id=<?php echo $client['client_id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'layout_footer.php'; ?>
