<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if client ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    die("Client ID is required");
}

$clientId = $_GET['id'];
$client = getClientById($clientId);

if (!$client) {
    header('Location: clients.php?error=Client not found');
    exit;
}

// Get client's policies
$policies = getClientPolicies($clientId);

// Get assigned agent info from policies if any
$agentInfo = null;
if (!empty($policies)) {
    // Get agent info from the first policy that has an agent
    foreach ($policies as $policy) {
        if (!empty($policy['agent_id'])) {
            $agentInfo = getAgentById($policy['agent_id']);
            break; // Use the first agent found
        }
    }
}

// Set page title
$pageTitle = 'CLIENT DETAILS';

// Include layout header
include 'layout.php';
?>

<style>
/* Reset any conflicting styles */
.detail-container {
    max-width: none;
    margin: 0;
    padding: 25px;
    background: transparent;
    box-shadow: none;
    border-radius: 0;
}

/* Main content styling */
.client-detail-container {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    padding: 25px;
    margin-bottom: 30px;
    margin-left:300px;
}

.client-header {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f2f5;
    color: #2c3e50;
    text-align: center;
}

/* Section containers */
.section-container {
    margin-bottom: 30px;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5eb;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e5eb;
}

/* Client info area */
.client-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
}

.client-details {
    flex: 1;
    min-width: 300px;
}

/* Detail rows */
.detail-row {
    display: flex;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f2f5;
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-label {
    flex: 0 0 180px;
    font-weight: 600;
    color: #5e6278;
}

.detail-value {
    flex: 1;
    color: #2c3e50;
}



/* Policy card styles */
.policy-card {
    background: #fff;
    border: 1px solid #e1e5eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s;
}

.policy-card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e5eb;
}

.policy-title {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
}

.policy-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-inactive {
    background-color: #ffebee;
    color: #c62828;
}

.status-pending {
    background-color: #fff3e0;
    color: #ef6c00;
}

.status-expired {
    background-color: #eceff1;
    color: #455a64;
}

.status-cancelled {
    background-color: #fafafa;
    color: #616161;
}

.status-lapse {
    background-color: #f8d7da;
    color: #721c24;
}

/* Policy status dropdown styles */
.policy-status-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.policy-status-dropdown {
    min-width: 120px;
}

.policy-status-dropdown .form-control {
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 20px;
    border: 2px solid #ddd;
    background-color: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.policy-status-dropdown .form-control:focus {
    outline: none;
    border-color: #e31b23;
    box-shadow: 0 0 0 0.2rem rgba(227, 27, 35, 0.25);
}

.policy-status-dropdown .form-control option[value="Active"] {
    background-color: #d4edda;
    color: #155724;
}

.policy-status-dropdown .form-control option[value="Lapse"] {
    background-color: #f8d7da;
    color: #721c24;
}

/* Status-based dropdown styling */
.status-select.status-active {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.status-select.status-lapse {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.status-select.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

/* Documents section styles */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.document-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.document-item:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.document-label {
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
}

.document-label i {
    color: #6c757d;
}

.document-action .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 20px;
}

.document-action .text-muted {
    font-size: 12px;
    font-style: italic;
}

/* Beneficiary styles */
.beneficiary-list {
    margin-top: 15px;
    border-top: 1px solid #e1e5eb;
    padding-top: 15px;
}

.beneficiary-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f2f5;
}

.beneficiary-item:last-child {
    border-bottom: none;
}

.beneficiary-info {
    flex-grow: 1;
}

.beneficiary-name {
    font-weight: 500;
    color: #2c3e50;
}

.beneficiary-details {
    font-size: 13px;
    color: #6c757d;
    margin-top: 5px;
}

.beneficiary-percentage {
    font-weight: 600;
    color: #2c3e50;
    margin-left: 15px;
}



.btn-edit {
    background-color: #e31b23; /* Matching the theme */
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-edit:hover {
    background-color: #c41920;
    color: white;
    text-decoration: none;
}

/* Action buttons container */
.action-btns {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .detail-row {
        flex-direction: column;
    }
    
    .detail-label {
        flex: none;
        margin-bottom: 5px;
    }
    
    .policy-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .policy-status {
        margin-top: 8px;
    }
    

}
</style>

<!-- Content area should be directly inside main-content div from layout.php -->
<div class="action-btns">
    <a href="clients.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left mr-2"></i> Back to Clients
    </a>
    <div>
        <a href="client_edit.php?id=<?php echo $client['client_id']; ?>" class="btn btn-primary">
            <i class="fas fa-edit mr-2"></i> Edit Client
        </a>
        <button class="btn btn-danger ml-2" onclick="confirmDelete('<?php echo $client['client_id']; ?>', '<?php echo htmlspecialchars($client['name']); ?>')">
            <i class="fas fa-trash mr-2"></i> Delete Client
        </button>
        <button onclick="window.print()" class="btn btn-info ml-2">
            <i class="fas fa-print mr-2"></i> Print
        </button>
    </div>
</div>

<div class="client-detail-container">
    <div class="client-header">
        CLIENT DETAILS
    </div>
    
    <div class="client-info">
        <div class="client-details">
            <div class="detail-row">
                <div class="detail-label">Name :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['name']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">IC Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['ic_number']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Client ID :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['client_id']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Gender :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['gender']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Date Of Birth :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['date_of_birth']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['phone_number']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Email :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['email']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Address :</div>
                <div class="detail-value"><?php echo htmlspecialchars($client['address']); ?></div>
            </div>
        </div>
    </div>
    
    <div class="section-container">
        <div class="section-title">Personal Details</div>
        <div class="detail-row">
            <div class="detail-label">Marital Status :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['marital_status'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Race :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['race'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Religion :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['religion'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Nationality :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['nationality'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Height :</div>
            <div class="detail-value"><?php echo !empty($client['height']) ? htmlspecialchars($client['height']) . ' cm' : 'Not specified'; ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Weight :</div>
            <div class="detail-value"><?php echo !empty($client['weight']) ? htmlspecialchars($client['weight']) . ' kg' : 'Not specified'; ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Smoker :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['smoker'] ?? 'Not specified'); ?></div>
        </div>
    </div>
    
    <div class="section-container">
        <div class="section-title">Employment Information</div>
        <div class="detail-row">
            <div class="detail-label">Occupation :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['occupation'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Exact Duties :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['exact_duties'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Nature of Business :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['nature_of_business'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Yearly Salary :</div>
            <div class="detail-value"><?php echo !empty($client['salary_yearly']) ? htmlspecialchars($client['salary_yearly']) : 'Not specified'; ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Company Name :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['company_name'] ?? 'Not specified'); ?></div>
        </div>
        <div class="detail-row">
            <div class="detail-label">Company Address :</div>
            <div class="detail-value"><?php echo htmlspecialchars($client['company_address'] ?? 'Not specified'); ?></div>
        </div>
    </div>

    <!-- Documents Section -->
    <div class="section-container">
        <div class="section-title">Client Documents</div>
        <div class="documents-grid">
            <div class="document-item">
                <div class="document-label">
                    <i class="fas fa-id-card mr-2"></i>NRIC
                </div>
                <div class="document-action">
                    <?php if (!empty($client['nric'])): ?>
                        <button class="btn btn-primary btn-sm" onclick="viewDocument('<?php echo htmlspecialchars($client['nric']); ?>', 'NRIC')">
                            <i class="fas fa-eye mr-1"></i>View PDF
                        </button>
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="document-item">
                <div class="document-label">
                    <i class="fas fa-signature mr-2"></i>Signature
                </div>
                <div class="document-action">
                    <?php if (!empty($client['signature'])): ?>
                        <button class="btn btn-primary btn-sm" onclick="viewDocument('<?php echo htmlspecialchars($client['signature']); ?>', 'Signature')">
                            <i class="fas fa-eye mr-1"></i>View PDF
                        </button>
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="document-item">
                <div class="document-label">
                    <i class="fas fa-credit-card mr-2"></i>Bank Card
                </div>
                <div class="document-action">
                    <?php if (!empty($client['bankcard'])): ?>
                        <button class="btn btn-primary btn-sm" onclick="viewDocument('<?php echo htmlspecialchars($client['bankcard']); ?>', 'Bank Card')">
                            <i class="fas fa-eye mr-1"></i>View PDF
                        </button>
                    <?php else: ?>
                        <span class="text-muted">Not uploaded</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="section-container">
        <div class="section-title">Policy Information</div>

        <?php if (empty($policies)): ?>
            <div class="alert alert-info mt-3">
                <p class="mb-0">No policies found for this client.</p>
            </div>
        <?php else: ?>
            <div class="mt-3">
                <div class="row">
                    <?php foreach ($policies as $policy): ?>
                        <div class="col-md-6 mb-4">
                            <div class="policy-card h-100">
                                <div class="policy-header">
                                    <div class="policy-title">
                                        <?php echo htmlspecialchars($policy['plan_type']); ?> -
                                        <?php echo isset($policy['policy_id']) ? htmlspecialchars($policy['policy_id']) : 'No Policy ID'; ?>
                                    </div>
                                    <div class="policy-status-container">
                                        <div class="policy-status-dropdown">
                                            <select class="form-control status-select" onchange="updatePolicyStatus('<?php echo htmlspecialchars($policy['policy_id']); ?>', this.value)">
                                                <?php
                                                // Include all possible statuses
                                                $statuses = ['Pending', 'Active', 'Lapse'];
                                                foreach ($statuses as $status):
                                                ?>
                                                    <option value="<?php echo $status; ?>" <?php echo ($policy['status'] === $status) ? 'selected' : ''; ?>>
                                                        <?php echo $status; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="detail-row">
                                    <div class="detail-label">Assigned Agent :</div>
                                    <div class="detail-value">
                                        <?php 
                                        if (!empty($policy['agent_id']) && !empty($policy['agent_name'])) {
                                            echo htmlspecialchars($policy['agent_name']) . ' (' . htmlspecialchars($policy['agent_id']) . ')';
                                        } elseif (!empty($policy['agent_id'])) {
                                            echo 'Agent ID: ' . htmlspecialchars($policy['agent_id']) . ' (Name not found)';
                                        } else {
                                            echo 'Not assigned';
                                        }
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="detail-row">
                                    <div class="detail-label">Sum Covered :</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($policy['sum_covered']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Coverage Term :</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($policy['coverage_term']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Contribution :</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($policy['contribution']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Start/End Date :</div>
                                    <div class="detail-value">
                                        <?php echo htmlspecialchars($policy['start_date']); ?> to 
                                        <?php echo htmlspecialchars($policy['end_date']); ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($policy['basic_plan_rider'])): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Basic Plan/Rider :</div>
                                        <div class="detail-value">
                                            <div class="text-wrap"><?php echo nl2br(htmlspecialchars($policy['basic_plan_rider'])); ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php 
                                // Check if the function exists before calling it
                                if (function_exists('getPolicyBeneficiaries')) {
                                    // Get beneficiaries for this policy
                                    $beneficiaries = getPolicyBeneficiaries($policy['policy_id']);
                                    if (!empty($beneficiaries)): 
                                    ?>
                                        <div class="beneficiary-list">
                                            <div class="section-title" style="font-size: 16px; margin-top: 20px;">Beneficiaries</div>
                                            <?php foreach ($beneficiaries as $beneficiary): ?>
                                                <div class="beneficiary-item">
                                                    <div class="beneficiary-info">
                                                        <div class="beneficiary-name"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                                        <div class="beneficiary-details">
                                                            IC: <?php echo htmlspecialchars($beneficiary['ic_number']); ?> | 
                                                            Relationship: <?php echo htmlspecialchars($beneficiary['relationship']); ?>
                                                        </div>
                                                    </div>
                                                    <div class="beneficiary-percentage"><?php echo htmlspecialchars($beneficiary['percentage']); ?>%</div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif;
                                } else {
                                    // Query beneficiaries using PDO
                                    try {
                                        $beneficiariesQuery = "SELECT * FROM beneficiaries WHERE policy_id = ?";
                                        $stmt = $pdo->prepare($beneficiariesQuery);
                                        $stmt->execute([$policy['policy_id']]);
                                        $beneficiaries = $stmt->fetchAll();

                                        if (!empty($beneficiaries)): ?>
                                            <div class="beneficiary-list">
                                                <div class="section-title" style="font-size: 16px; margin-top: 20px;">Beneficiaries</div>
                                                <?php foreach ($beneficiaries as $beneficiary): ?>
                                                    <div class="beneficiary-item">
                                                        <div class="beneficiary-info">
                                                            <div class="beneficiary-name"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                                            <div class="beneficiary-details">
                                                                IC: <?php echo htmlspecialchars($beneficiary['ic_number']); ?> |
                                                                Relationship: <?php echo htmlspecialchars($beneficiary['relationship']); ?>
                                                            </div>
                                                        </div>
                                                        <div class="beneficiary-percentage"><?php echo htmlspecialchars($beneficiary['percentage']); ?>%</div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif;
                                    } catch (PDOException $e) {
                                        // Silently handle error - beneficiaries are optional
                                        error_log("Error fetching beneficiaries: " . $e->getMessage());
                                    }
                                }
                                ?>
                                

                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add JavaScript for delete confirmation dialog -->
<script>
function confirmDelete(clientId, clientName) {
    if (confirm('Are you sure you want to delete client: ' + clientName + '? This action cannot be undone.')) {
        deleteClient(clientId);
    }
}

function deleteClient(clientId) {
    fetch('delete_client.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            client_id: clientId
        })
    })
    .then(response => response.json())
    .then(data => {
        // Silently redirect on both success and failure
        window.location.href = 'clients.php';
    })
    .catch(error => {
        console.error('Error:', error);
        // Still redirect
        window.location.href = 'clients.php';
    });
}

function updatePolicyStatus(policyId, newStatus) {
    // Get the dropdown element
    const dropdown = document.querySelector(`select[onchange*="${policyId}"]`);
    const originalValue = dropdown.value;

    // Disable dropdown during update
    dropdown.disabled = true;
    dropdown.style.opacity = '0.6';

    fetch('update_policy_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            policy_id: policyId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Re-enable dropdown
            dropdown.disabled = false;
            dropdown.style.opacity = '1';

            // Update dropdown styling based on status
            dropdown.className = 'form-control status-select status-' + newStatus.toLowerCase();

            // Show success message briefly
            const tempMessage = document.createElement('div');
            tempMessage.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #d4edda; color: #155724; padding: 10px 15px; border-radius: 5px; z-index: 1000; border: 1px solid #c3e6cb;';
            tempMessage.textContent = 'Policy status updated successfully!';
            document.body.appendChild(tempMessage);

            setTimeout(() => {
                document.body.removeChild(tempMessage);
            }, 3000);
        } else {
            // Restore original value and show error
            dropdown.value = originalValue;
            dropdown.disabled = false;
            dropdown.style.opacity = '1';
            alert('Error updating status: ' + data.message);
        }
    })
    .catch(error => {
        // Restore original value and show error
        dropdown.value = originalValue;
        dropdown.disabled = false;
        dropdown.style.opacity = '1';
        console.error('Error:', error);
        alert('Network error occurred while updating status');
    });
}

// Initialize dropdown styling based on current values
document.addEventListener('DOMContentLoaded', function() {
    const statusDropdowns = document.querySelectorAll('.status-select');
    statusDropdowns.forEach(function(dropdown) {
        const currentStatus = dropdown.value.toLowerCase();
        dropdown.className = 'form-control status-select status-' + currentStatus;
    });
});

// Function to view documents
function viewDocument(documentUrl, documentType) {
    if (!documentUrl) {
        alert('Document URL is not available');
        return;
    }

    // Open the document in a new tab/window
    window.open(documentUrl, '_blank');
}

</script>

<?php include 'layout_footer.php'; ?> 