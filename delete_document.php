<?php
require_once 'config.php';
session_start();

if (!isset($_SESSION['user_id'])) {
    die(json_encode(['success' => false, 'message' => 'Unauthorized']));
}

$data = json_decode(file_get_contents('php://input'), true);
$documentId = $data['document_id'] ?? null;

try {
    if (!$documentId) {
        throw new Exception('Document ID is required');
    }

    // Get document info first to delete the file
    $stmt = $pdo->prepare("SELECT file_path FROM agent_documents WHERE document_id = ?");
    $stmt->execute([$documentId]);
    $document = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($document && file_exists($document['file_path'])) {
        unlink($document['file_path']);
    }

    // Delete the database dwrecord
    $stmt = $pdo->prepare("DELETE FROM agent_documents WHERE document_id = ?");
    $stmt->execute([$documentId]);

    echo json_encode(['success' => true]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 