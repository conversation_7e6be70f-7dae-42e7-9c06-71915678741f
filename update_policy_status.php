<?php
/**
 * Update Policy Status - AJAX Handler
 * Allows admins to change policy status between Active and Lapse
 * Note: Pending policies are set by agents and cannot be changed via this endpoint
 */

session_start();
require_once 'config.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Please log in.'
    ]);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Use POST.'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['policy_id']) || !isset($input['status'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Missing required fields: policy_id and status'
    ]);
    exit;
}

$policyId = trim($input['policy_id']);
$newStatus = trim($input['status']);

// Validate policy ID
if (empty($policyId)) {
    echo json_encode([
        'success' => false,
        'message' => 'Policy ID cannot be empty'
    ]);
    exit;
}

// Validate status - only allow Active and Lapse for admin changes
$allowedStatuses = ['Active', 'Lapse'];
if (!in_array($newStatus, $allowedStatuses)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid status. Admin can only set status to: ' . implode(', ', $allowedStatuses)
    ]);
    exit;
}

try {
    // Check if policy exists
    $checkStmt = $pdo->prepare("SELECT policy_id, status, client_id FROM policies WHERE policy_id = ?");
    $checkStmt->execute([$policyId]);
    $policy = $checkStmt->fetch();
    
    if (!$policy) {
        echo json_encode([
            'success' => false,
            'message' => 'Policy not found'
        ]);
        exit;
    }
    
    $oldStatus = $policy['status'];
    
    // Check if status is actually changing
    if ($oldStatus === $newStatus) {
        echo json_encode([
            'success' => false,
            'message' => 'Policy is already in ' . $newStatus . ' status'
        ]);
        exit;
    }
    
    // Update policy status
    $updateStmt = $pdo->prepare("UPDATE policies SET status = ?, updated_at = NOW() WHERE policy_id = ?");
    $result = $updateStmt->execute([$newStatus, $policyId]);
    
    if ($result) {
        // Log the status change
        error_log("Policy status updated: Policy ID: $policyId, From: $oldStatus, To: $newStatus, By: " . $_SESSION['username']);
        
        // Optional: Insert into audit log table if you have one
        // $auditStmt = $pdo->prepare("INSERT INTO policy_audit_log (policy_id, old_status, new_status, changed_by, changed_at) VALUES (?, ?, ?, ?, NOW())");
        // $auditStmt->execute([$policyId, $oldStatus, $newStatus, $_SESSION['user_id']]);
        
        echo json_encode([
            'success' => true,
            'message' => "Policy status successfully changed from $oldStatus to $newStatus",
            'data' => [
                'policy_id' => $policyId,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update policy status in database'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in update_policy_status.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred while updating policy status'
    ]);
    
} catch (Exception $e) {
    error_log("General error in update_policy_status.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred'
    ]);
}

?>
